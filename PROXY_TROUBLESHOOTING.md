# 代理环境下的连接问题排查指南

## 问题描述

在设置了 `https_proxy` 或 `http_proxy` 环境变量的情况下，连接 backtest 的 HTTPS 或 WSS 服务器时出现连接失败错误：

```
error sending request for url (https://127.0.0.1:8083/fapi/v1/exchangeInfo)
```

## 问题原因

### 1. 代理服务器限制本地连接
大多数代理服务器出于安全考虑，会拒绝代理到本地地址：
- `127.0.0.1`、`localhost`、`::1` 等回环地址
- 私有网络地址（`192.168.x.x`、`10.x.x.x`、`172.16.x.x-172.31.x.x`）

### 2. HTTPS CONNECT 方法支持问题
- HTTP 代理可能不支持 HTTPS 的 CONNECT 隧道方法
- 代理服务器配置可能禁用了 CONNECT 方法

### 3. TLS 证书验证复杂性
- 通过代理的 TLS 握手过程更复杂
- 自签名证书在代理环境下验证困难

## 解决方案

### 方案 1：绕过代理（推荐）

设置 `no_proxy` 环境变量，排除本地地址：

```bash
# 临时设置（当前会话）
export no_proxy="localhost,127.0.0.1,::1,0.0.0.0"
export NO_PROXY="localhost,127.0.0.1,::1,0.0.0.0"

# 永久设置（添加到 ~/.bashrc 或 ~/.zshrc）
echo 'export no_proxy="localhost,127.0.0.1,::1,0.0.0.0"' >> ~/.bashrc
echo 'export NO_PROXY="localhost,127.0.0.1,::1,0.0.0.0"' >> ~/.bashrc
```

### 方案 2：使用外部可访问地址

1. **服务器绑定到所有接口**（已修改）：
   - HTTP 服务器现在绑定到 `0.0.0.0:8083`
   - WebSocket 服务器现在绑定到 `0.0.0.0:8082`

2. **使用机器的外部 IP 地址**：
   ```bash
   # 查找机器的 IP 地址
   ip addr show | grep 'inet ' | grep -v '127.0.0.1'
   
   # 或者使用
   hostname -I
   ```

3. **连接到外部地址**：
   ```bash
   # 假设机器 IP 是 *************
   curl https://*************:8083/api/v1/health
   ```

### 方案 3：临时禁用代理

```bash
# 临时禁用所有代理
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY

# 运行测试
cargo run --bin backtest -- --config test_tls_config.toml

# 重新启用代理（如果需要）
export http_proxy="your_proxy_url"
export https_proxy="your_proxy_url"
```

### 方案 4：代理服务器配置

如果您控制代理服务器，可以配置允许本地连接：

**Squid 代理示例配置**：
```
# 允许连接到本地地址
acl localnet src *********/8
acl localnet src ::1/128
http_access allow localnet

# 允许 CONNECT 方法到本地端口
acl SSL_ports port 8082 8083
acl CONNECT method CONNECT
http_access allow CONNECT SSL_ports localnet
```

## 测试和验证

### 1. 运行代理测试客户端
```bash
cargo run --bin test_proxy_client
```

### 2. 手动测试连接
```bash
# 测试 HTTP 连接
curl -v https://localhost:8083/api/v1/health

# 测试 WebSocket 连接
cargo run --bin test_ca_client
```

### 3. 检查代理设置
```bash
echo "HTTP_PROXY: $http_proxy"
echo "HTTPS_PROXY: $https_proxy"
echo "NO_PROXY: $no_proxy"
```

## 常见错误和解决方法

### 错误 1: "Connection refused"
- **原因**: 代理拒绝连接到本地地址
- **解决**: 设置 `no_proxy` 或使用外部地址

### 错误 2: "TLS handshake failed"
- **原因**: 代理干扰 TLS 握手
- **解决**: 绕过代理或使用 HTTP 而非 HTTPS

### 错误 3: "Proxy authentication required"
- **原因**: 代理需要认证
- **解决**: 在代理 URL 中包含认证信息或绕过代理

## 最佳实践

1. **开发环境**: 始终设置 `no_proxy` 包含本地地址
2. **生产环境**: 使用外部可访问的地址和域名
3. **测试**: 提供多种连接方式的测试工具
4. **文档**: 明确说明代理环境下的配置要求

## 相关文件

- `src/bin/test_proxy_client.rs` - 代理环境测试客户端
- `src/http/server.rs` - HTTP 服务器（已修改绑定地址）
- `src/websocket/server.rs` - WebSocket 服务器（已修改绑定地址）
