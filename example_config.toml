# Backtest Framework Configuration with TLS enabled
# 这是一个启用TLS的测试配置文件

# 交易所设置
exchange = "Binance"

# 回测时间范围
start_time = "2025-07-07T11:23:20Z"
end_time = "2025-07-07T11:25:00Z"

# 服务器端口配置
websocket_port = 8082
http_port = 8083

# 日志配置
log_level = "info"

# 性能配置
performance_target_us = 500

# HTTP服务器TLS配置
[http_tls]
# 启用TLS（HTTPS）
enabled = true

# 使用证书文件
[http_tls.cert_source]
type = "Files"
cert_path = "./certs/server.crt"
key_path = "./certs/server.key"

# WebSocket服务器TLS配置
[websocket_tls]
# 启用TLS（WSS）
enabled = true

# 使用证书文件
[websocket_tls.cert_source]
type = "Files"
cert_path = "./certs/server.crt"
key_path = "./certs/server.key"

# 数据路径配置
[data_paths]
# 数据根目录（确保此目录存在）
root = "./data"
