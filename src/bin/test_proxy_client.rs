use backtest::Result;
use std::env;
use tokio_tungstenite::{connect_async, tungstenite::Message};
use futures_util::{SinkExt, StreamExt};
use tracing::{info, warn, error};

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 检查代理环境变量
    check_proxy_settings();

    // 测试不同的连接方式
    test_direct_connection().await?;
    test_proxy_bypass().await?;
    test_external_address().await?;

    Ok(())
}

fn check_proxy_settings() {
    info!("检查代理设置...");
    
    if let Ok(http_proxy) = env::var("http_proxy") {
        info!("HTTP_PROXY: {}", http_proxy);
    }
    
    if let Ok(https_proxy) = env::var("https_proxy") {
        info!("HTTPS_PROXY: {}", https_proxy);
    }
    
    if let Ok(no_proxy) = env::var("no_proxy") {
        info!("NO_PROXY: {}", no_proxy);
    } else {
        warn!("NO_PROXY 未设置，建议设置为 'localhost,127.0.0.1,::1'");
    }
}

async fn test_direct_connection() -> Result<()> {
    info!("测试直接连接到 localhost...");
    
    match connect_async("ws://127.0.0.1:8082").await {
        Ok((ws_stream, _)) => {
            info!("✓ 直接连接成功");
            let (mut write, mut read) = ws_stream.split();
            
            // 发送测试消息
            write.send(Message::Text("ping".to_string())).await.map_err(|e| {
                backtest::BacktestError::WebSocket(format!("发送消息失败: {}", e))
            })?;
            
            // 接收响应
            if let Some(msg) = read.next().await {
                match msg {
                    Ok(Message::Text(text)) => info!("收到响应: {}", text),
                    Ok(_) => info!("收到非文本消息"),
                    Err(e) => error!("接收消息错误: {}", e),
                }
            }
        }
        Err(e) => {
            error!("❌ 直接连接失败: {}", e);
        }
    }
    
    Ok(())
}

async fn test_proxy_bypass() -> Result<()> {
    info!("测试绕过代理连接...");
    
    // 临时设置 no_proxy
    env::set_var("no_proxy", "localhost,127.0.0.1,::1");
    env::set_var("NO_PROXY", "localhost,127.0.0.1,::1");
    
    match connect_async("ws://127.0.0.1:8082").await {
        Ok((ws_stream, _)) => {
            info!("✓ 绕过代理连接成功");
            let (mut write, mut read) = ws_stream.split();
            
            write.send(Message::Text("ping_bypass".to_string())).await.map_err(|e| {
                backtest::BacktestError::WebSocket(format!("发送消息失败: {}", e))
            })?;
            
            if let Some(msg) = read.next().await {
                match msg {
                    Ok(Message::Text(text)) => info!("收到响应: {}", text),
                    Ok(_) => info!("收到非文本消息"),
                    Err(e) => error!("接收消息错误: {}", e),
                }
            }
        }
        Err(e) => {
            error!("❌ 绕过代理连接失败: {}", e);
        }
    }
    
    Ok(())
}

async fn test_external_address() -> Result<()> {
    info!("测试外部地址连接...");
    
    // 尝试连接到 0.0.0.0 绑定的服务器
    // 注意：这需要服务器绑定到 0.0.0.0 而不是 127.0.0.1
    let external_urls = vec![
        "ws://0.0.0.0:8082",
        // 如果有外部IP，也可以尝试
        // "ws://YOUR_EXTERNAL_IP:8082",
    ];
    
    for url in external_urls {
        info!("尝试连接: {}", url);
        match connect_async(url).await {
            Ok((ws_stream, _)) => {
                info!("✓ 外部地址连接成功: {}", url);
                let (mut write, mut read) = ws_stream.split();
                
                write.send(Message::Text("ping_external".to_string())).await.map_err(|e| {
                    backtest::BacktestError::WebSocket(format!("发送消息失败: {}", e))
                })?;
                
                if let Some(msg) = read.next().await {
                    match msg {
                        Ok(Message::Text(text)) => info!("收到响应: {}", text),
                        Ok(_) => info!("收到非文本消息"),
                        Err(e) => error!("接收消息错误: {}", e),
                    }
                }
                break;
            }
            Err(e) => {
                warn!("❌ 外部地址连接失败 {}: {}", url, e);
            }
        }
    }
    
    Ok(())
}
