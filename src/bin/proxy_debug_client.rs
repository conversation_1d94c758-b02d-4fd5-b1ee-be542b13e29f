use reqwest;
use std::env;
use std::error::Error;
use tracing::{error, info, warn};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("🔍 代理调试客户端启动");

    // 显示当前代理环境变量
    show_proxy_env();

    // 测试不同的连接方式
    test_direct_connection().await?;
    test_proxy_connection().await?;
    test_external_ip_connection().await?;
    test_proxy_with_external_ip().await?;

    Ok(())
}

fn show_proxy_env() {
    info!("📋 当前代理环境变量:");

    let proxy_vars = [
        "http_proxy",
        "https_proxy",
        "HTTP_PROXY",
        "HTTPS_PROXY",
        "no_proxy",
        "NO_PROXY",
        "all_proxy",
        "ALL_PROXY",
    ];

    for var in &proxy_vars {
        match env::var(var) {
            Ok(value) => info!("  {}: {}", var, value),
            Err(_) => info!("  {}: (未设置)", var),
        }
    }
}

async fn test_direct_connection() -> Result<(), Box<dyn std::error::Error>> {
    info!("🔗 测试 1: 直接连接 (无代理)");

    let client = reqwest::Client::builder()
        .no_proxy()
        .danger_accept_invalid_certs(true)
        .timeout(std::time::Duration::from_secs(10))
        .build()?;

    info!("Client 配置: {:?}", client);

    match client
        .get("https://127.0.0.1:8083/fapi/v1/exchangeInfo")
        .send()
        .await
    {
        Ok(response) => {
            info!("✅ 直接连接成功! 状态: {}", response.status());
            let text = response.text().await?;
            info!("响应长度: {} 字符", text.len());
        }
        Err(e) => {
            error!("❌ 直接连接失败: {}", e);
            error!("错误类型: {:?}", e);
        }
    }

    Ok(())
}

async fn test_proxy_connection() -> Result<(), Box<dyn std::error::Error>> {
    info!("🔗 测试 2: 通过代理连接 localhost");

    let client = reqwest::Client::builder()
        .danger_accept_invalid_certs(true)
        .timeout(std::time::Duration::from_secs(10))
        .build()?;

    info!("Client 配置: {:?}", client);

    match client
        .get("https://127.0.0.1:8083/fapi/v1/exchangeInfo")
        .send()
        .await
    {
        Ok(response) => {
            info!("✅ 代理连接 localhost 成功! 状态: {}", response.status());
            let text = response.text().await?;
            info!("响应长度: {} 字符", text.len());
        }
        Err(e) => {
            error!("❌ 代理连接 localhost 失败: {}", e);
            error!("错误类型: {:?}", e);

            // 尝试获取更详细的错误信息
            if let Some(source) = e.source() {
                error!("根本原因: {}", source);
            }
        }
    }

    Ok(())
}

async fn test_external_ip_connection() -> Result<(), Box<dyn std::error::Error>> {
    info!("🔗 测试 3: 直接连接外部 IP (无代理)");

    let client = reqwest::Client::builder()
        .no_proxy()
        .danger_accept_invalid_certs(true)
        .timeout(std::time::Duration::from_secs(10))
        .build()?;

    match client
        .get("https://************:8083/fapi/v1/exchangeInfo")
        .send()
        .await
    {
        Ok(response) => {
            info!("✅ 直接连接外部 IP 成功! 状态: {}", response.status());
            let text = response.text().await?;
            info!("响应长度: {} 字符", text.len());
        }
        Err(e) => {
            error!("❌ 直接连接外部 IP 失败: {}", e);
        }
    }

    Ok(())
}

async fn test_proxy_with_external_ip() -> Result<(), Box<dyn std::error::Error>> {
    info!("🔗 测试 4: 通过代理连接外部 IP");

    let client = reqwest::Client::builder()
        .danger_accept_invalid_certs(true)
        .timeout(std::time::Duration::from_secs(10))
        .build()?;

    info!("Client 配置: {:?}", client);

    match client
        .get("https://************:8083/fapi/v1/exchangeInfo")
        .send()
        .await
    {
        Ok(response) => {
            info!("✅ 代理连接外部 IP 成功! 状态: {}", response.status());
            let text = response.text().await?;
            info!("响应长度: {} 字符", text.len());
        }
        Err(e) => {
            error!("❌ 代理连接外部 IP 失败: {}", e);
            error!("错误类型: {:?}", e);

            if let Some(source) = e.source() {
                error!("根本原因: {}", source);
            }
        }
    }

    Ok(())
}

async fn test_proxy_configuration() -> Result<(), Box<dyn std::error::Error>> {
    info!("🔗 测试 5: 手动配置代理");

    // 如果您知道代理地址，可以手动配置
    if let Ok(proxy_url) = env::var("https_proxy") {
        info!("使用代理: {}", proxy_url);

        let proxy = reqwest::Proxy::https(&proxy_url)?;
        let client = reqwest::Client::builder()
            .proxy(proxy)
            .danger_accept_invalid_certs(true)
            .timeout(std::time::Duration::from_secs(10))
            .build()?;

        match client
            .get("https://************:8083/fapi/v1/exchangeInfo")
            .send()
            .await
        {
            Ok(response) => {
                info!("✅ 手动代理配置成功! 状态: {}", response.status());
            }
            Err(e) => {
                error!("❌ 手动代理配置失败: {}", e);
            }
        }
    } else {
        warn!("⚠️  未找到 https_proxy 环境变量，跳过手动代理测试");
    }

    Ok(())
}
